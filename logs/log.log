2025-06-02 18:46:10,127 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:46:10,129 - root - INFO - Log<PERSON>le configured
2025-06-02 18:46:10,131 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:46:10,135 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-06-02 18:46:10,137 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:46:10,140 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-06-02 18:46:10,143 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.dryrun.sqlite"
2025-06-02 18:46:10,144 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:46:10,190 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:46:10,193 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:46:10,198 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:46:10,218 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:46:10,220 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:46:10,225 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:46:10,228 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:46:10,230 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:46:10,232 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:46:10,234 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:46:10,236 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:46:10,238 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:46:10,240 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:46:10,242 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:46:10,243 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:46:10,245 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:46:10,247 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:46:10,249 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:46:10,251 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:46:10,254 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:46:10,255 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:46:10,257 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:46:10,259 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:46:10,260 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:46:10,262 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:46:10,264 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:46:10,266 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:46:10,268 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:46:10,269 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:46:10,271 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:46:10,273 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:46:10,275 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:46:10,277 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:46:10,279 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:46:10,280 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:46:10,282 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:46:10,284 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:46:10,285 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:46:10,288 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:46:10,336 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-06-02 18:46:10,338 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:46:10,340 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:46:10,357 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:46:10,377 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:46:14,419 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:46:14,598 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 18:46:14,600 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 18:46:15,271 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 18:46:16,206 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 18:46:16,208 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 18:46:16,210 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 18:46:16,302 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 18:46:16,311 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 18:46:16,321 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 18:46:16,323 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 18:46:16,328 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 18:46:16,331 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 18:46:16,333 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 18:46:16,336 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 18:46:16,337 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 18:46:16,339 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-06-02 18:46:16,382 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-06-02 18:46:16,386 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 18:46:16,389 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 18:46:16,525 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 18:46:16,676 - telegram.ext.Application - INFO - Application started
2025-06-02 18:46:21,401 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:47:21,406 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:48:12,129 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-06-02 18:48:12,132 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-06-02 18:48:12,135 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 18:48:12,145 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 18:48:12,148 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-02 18:48:12,151 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-02 18:48:12,310 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-02 18:48:12,411 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-06-02 18:48:12,415 - telegram.ext.Application - INFO - Application.stop() complete
2025-06-02 18:48:17,057 - freqtrade - INFO - SIGINT received, aborting ...
2025-06-02 18:48:29,543 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:48:29,546 - root - INFO - Logfile configured
2025-06-02 18:48:29,548 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:48:29,550 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 18:48:29,552 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:48:29,554 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 18:48:29,555 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 18:48:29,557 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:48:29,577 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:48:29,580 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:48:29,585 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:48:29,613 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:48:29,615 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:48:29,621 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:48:29,623 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:48:29,626 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:48:29,628 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:48:29,630 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:48:29,632 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:48:29,634 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:48:29,636 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:48:29,639 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:48:29,640 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:48:29,643 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:48:29,646 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:48:29,648 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:48:29,650 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:48:29,652 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:48:29,654 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:48:29,655 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:48:29,657 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:48:29,659 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:48:29,661 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:48:29,663 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:48:29,664 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:48:29,666 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:48:29,668 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:48:29,670 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:48:29,671 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:48:29,673 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:48:29,675 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:48:29,677 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:48:29,678 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:48:29,680 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:48:29,682 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:48:29,683 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:48:29,685 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:48:29,729 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:48:29,731 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:48:29,749 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:48:29,769 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:48:33,804 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:48:33,985 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-02 18:48:33,987 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-02 18:48:33,989 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-02 18:48:33,992 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-02 18:48:33,994 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-02 18:48:33,996 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-02 18:49:59,602 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:49:59,603 - root - INFO - Logfile configured
2025-06-02 18:49:59,606 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:49:59,609 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 18:49:59,611 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:49:59,612 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 18:49:59,614 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 18:49:59,615 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:49:59,649 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:49:59,651 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:49:59,655 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:49:59,675 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:49:59,677 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:49:59,686 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:49:59,689 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:49:59,693 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:49:59,695 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:49:59,698 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:49:59,700 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:49:59,703 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:49:59,706 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:49:59,708 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:49:59,711 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:49:59,713 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:49:59,715 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:49:59,718 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:49:59,720 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:49:59,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:49:59,725 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:49:59,727 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:49:59,730 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:49:59,732 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:49:59,735 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:49:59,737 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:49:59,740 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:49:59,742 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:49:59,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:49:59,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:49:59,749 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:49:59,752 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:49:59,753 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:49:59,755 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:49:59,757 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:49:59,759 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:49:59,761 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:49:59,762 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:49:59,764 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:49:59,801 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:49:59,803 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:49:59,821 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:49:59,842 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:50:03,830 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:50:03,965 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-02 18:50:03,967 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-02 18:50:03,969 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-02 18:50:03,971 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-02 18:50:03,973 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-02 18:50:03,976 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-02 18:51:34,722 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:51:34,724 - root - INFO - Logfile configured
2025-06-02 18:51:34,726 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:51:34,728 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 18:51:34,730 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:51:34,732 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 18:51:34,733 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 18:51:34,735 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:51:34,765 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:51:34,767 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:51:34,772 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:51:34,791 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:51:34,793 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:51:34,798 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:51:34,800 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:51:34,802 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:51:34,804 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:51:34,806 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:51:34,808 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:51:34,810 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:51:34,812 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:51:34,814 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:51:34,815 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:51:34,817 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:51:34,819 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:51:34,820 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:51:34,822 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:51:34,824 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:51:34,826 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:51:34,827 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:51:34,829 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:51:34,830 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:51:34,833 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:51:34,834 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:51:34,836 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:51:34,838 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:51:34,839 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:51:34,841 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:51:34,843 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:51:34,844 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:51:34,846 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:51:34,848 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:51:34,851 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:51:34,853 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:51:34,854 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:51:34,856 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:51:34,858 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:51:34,903 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:51:34,905 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:51:34,924 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:51:34,945 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:51:39,094 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:51:39,211 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-02 18:51:39,215 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-02 18:51:39,219 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-02 18:51:39,222 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-02 18:51:39,225 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-02 18:51:39,229 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-02 18:52:00,579 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:52:00,581 - root - INFO - Logfile configured
2025-06-02 18:52:00,584 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:52:00,587 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 18:52:00,590 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:52:00,592 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 18:52:00,594 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 18:52:00,596 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:52:00,632 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:52:00,636 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:52:00,642 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:52:00,669 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:52:00,672 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:52:00,681 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:52:00,684 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:52:00,688 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:52:00,690 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:52:00,693 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:52:00,696 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:52:00,699 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:52:00,701 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:52:00,704 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:52:00,707 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:52:00,709 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:52:00,712 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:52:00,715 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:52:00,717 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:52:00,720 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:52:00,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:52:00,725 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:52:00,728 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:52:00,730 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:52:00,734 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:52:00,736 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:52:00,739 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:52:00,741 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:52:00,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:52:00,745 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:52:00,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:52:00,748 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:52:00,750 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:52:00,751 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:52:00,753 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:52:00,754 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:52:00,756 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:52:00,758 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:52:00,761 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:52:00,799 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:52:00,802 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:52:00,823 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:52:00,844 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:52:05,093 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:52:05,239 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-02 18:52:05,243 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-02 18:52:05,247 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-02 18:52:05,250 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-02 18:52:05,253 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-02 18:52:05,256 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-02 18:55:31,548 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 18:55:31,550 - root - INFO - Logfile configured
2025-06-02 18:55:31,552 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 18:55:31,554 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 18:55:31,556 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 18:55:31,557 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 18:55:31,559 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 18:55:31,561 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 18:55:31,595 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 18:55:31,597 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 18:55:31,601 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 18:55:31,620 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 18:55:31,622 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 18:55:31,628 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 18:55:31,630 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 18:55:31,632 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 18:55:31,634 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 18:55:31,636 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 18:55:31,637 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 18:55:31,639 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 18:55:31,641 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 18:55:31,643 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 18:55:31,644 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 18:55:31,646 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 18:55:31,648 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 18:55:31,649 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 18:55:31,651 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 18:55:31,652 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 18:55:31,654 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 18:55:31,655 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 18:55:31,657 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 18:55:31,659 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 18:55:31,660 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 18:55:31,662 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 18:55:31,664 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 18:55:31,666 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 18:55:31,667 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 18:55:31,669 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 18:55:31,670 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 18:55:31,672 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 18:55:31,674 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 18:55:31,675 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 18:55:31,677 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 18:55:31,678 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 18:55:31,681 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 18:55:31,684 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 18:55:31,687 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 18:55:31,729 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 18:55:31,731 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:55:31,749 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 18:55:31,768 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 18:55:35,712 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 18:55:36,543 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 18:55:36,545 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 18:55:37,244 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 18:55:38,172 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 18:55:38,174 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 18:55:38,176 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 18:55:38,281 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 18:55:38,283 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 18:55:38,291 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 18:55:38,293 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 18:55:38,298 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 18:55:38,301 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 18:55:38,304 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 18:55:38,306 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 18:55:38,309 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 18:55:38,313 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-06-02 18:55:38,362 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 18:55:38,364 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 18:55:38,387 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 18:55:38,462 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 18:55:38,582 - telegram.ext.Application - INFO - Application started
2025-06-02 18:55:43,391 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:56:43,396 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:57:43,400 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:58:43,405 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 18:59:43,411 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:00:04,985 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:06,546 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:11,559 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:16,585 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:21,583 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:26,553 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:31,549 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:36,557 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:41,659 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:00:46,615 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:51,547 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:00:56,565 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:01,562 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:06,550 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:07,376 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:11,562 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:16,556 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:21,534 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:26,535 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:31,538 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:36,562 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:41,529 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:01:46,682 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:51,632 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:01:56,533 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:01,740 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:06,537 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:11,601 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:16,732 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:21,539 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:26,542 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:31,529 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:36,581 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:41,558 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:46,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:02:46,534 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:51,559 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:02:56,546 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:01,568 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:06,586 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:11,608 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:16,589 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:21,613 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:26,540 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:31,535 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:36,559 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:41,559 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:03:46,560 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:51,573 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:03:56,555 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:01,537 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:06,539 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:11,569 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:16,596 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:21,618 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:26,544 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:31,619 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:36,588 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:41,548 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:46,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:04:46,606 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:51,592 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:04:56,602 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:05:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:06:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:07:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:08:26,015 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'reload_config'}
2025-06-02 19:08:26,018 - freqtrade.worker - INFO - Changing state from RUNNING to: RELOAD_CONFIG
2025-06-02 19:08:26,027 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RELOAD_CONFIG'
2025-06-02 19:08:26,031 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:08:26,038 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:08:26,041 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-02 19:08:26,044 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-02 19:08:26,160 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-02 19:08:29,420 - freqtrade.configuration.load_config - INFO - Using config: user_data/config.json ...
2025-06-02 19:08:29,436 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:08:29,438 - root - INFO - Logfile configured
2025-06-02 19:08:29,440 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:08:29,442 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:08:29,444 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:08:29,447 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:08:29,450 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:08:29,452 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:08:29,496 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:08:29,499 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:08:29,501 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:08:29,521 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:08:29,523 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:08:29,528 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 19:08:29,530 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 19:08:29,532 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 19:08:29,534 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 19:08:29,536 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 19:08:29,537 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 19:08:29,539 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 19:08:29,541 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 19:08:29,542 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 19:08:29,544 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 19:08:29,546 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 19:08:29,547 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 19:08:29,549 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 19:08:29,550 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 19:08:29,552 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 19:08:29,553 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 19:08:29,555 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 19:08:29,557 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 19:08:29,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60, 'emergency_exit': 'market'}
2025-06-02 19:08:29,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 19:08:29,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 19:08:29,564 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 19:08:29,566 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 19:08:29,568 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 19:08:29,569 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 19:08:29,571 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 19:08:29,572 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 19:08:29,574 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 19:08:29,576 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 19:08:29,578 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 19:08:29,580 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 19:08:29,582 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 19:08:29,583 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 19:08:29,585 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 19:08:29,596 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 19:08:29,598 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:08:29,615 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:08:29,634 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 19:08:33,658 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 19:08:34,210 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:08:34,212 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 19:08:34,214 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 19:08:34,454 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 19:08:34,456 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 19:08:34,458 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 19:08:34,510 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 19:08:34,512 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 19:08:34,515 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 19:08:34,522 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 19:08:34,526 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 19:08:34,530 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 19:08:34,533 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 19:08:34,535 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 19:08:34,537 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 19:08:34,540 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running after config reloaded'}
2025-06-02 19:08:34,542 - freqtrade.worker - INFO - Changing state from RELOAD_CONFIG to: RUNNING
2025-06-02 19:08:34,564 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:08:34,567 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:08:34,603 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:08:34,650 - telegram.ext.Application - INFO - Application started
2025-06-02 19:08:39,608 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:08:53,458 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:08:53,724 - freqtrade.rpc.telegram - ERROR - Forcebuy error!
Traceback (most recent call last):
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1374, in _force_enter_action
    await loop.run_in_executor(None, _force_enter)
  File "/usr/local/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 74, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1370, in _force_enter
    self._rpc._rpc_force_entry(pair, price, order_side=order_side)
  File "/freqtrade/freqtrade/rpc/rpc.py", line 1056, in _rpc_force_entry
    raise RPCException(f"Failed to enter position for {pair}.")
freqtrade.rpc.rpc.RPCException: Failed to enter position for ETH/USDC:USDC.
2025-06-02 19:09:39,612 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:10:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:11:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:12:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:13:41,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:14:41,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:15:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:16:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:17:46,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:18:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:19:46,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:20:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:21:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:22:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:23:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:24:51,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:25:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:26:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:27:46,011 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'stopped'}
2025-06-02 19:27:46,013 - freqtrade.worker - INFO - Changing state from RUNNING to: STOPPED
2025-06-02 19:27:51,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-02 19:28:26,024 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 19:28:26,027 - freqtrade.worker - INFO - Changing state from STOPPED to: RUNNING
2025-06-02 19:28:26,042 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:28:26,045 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:28:26,057 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:28:31,061 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:28:42,032 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:28:42,289 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 15.772200000000002 and price: 2539.0 ...
2025-06-02 19:28:43,049 - freqtrade.exchange.common - WARNING - set_margin_mode() returned exception: "Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}". Retrying still for 4 times.
2025-06-02 19:28:43,890 - freqtrade.exchange.common - WARNING - set_margin_mode() returned exception: "Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}". Retrying still for 3 times.
2025-06-02 19:28:44,655 - freqtrade.exchange.common - WARNING - set_margin_mode() returned exception: "Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}". Retrying still for 2 times.
2025-06-02 19:28:45,423 - freqtrade.exchange.common - WARNING - set_margin_mode() returned exception: "Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}". Retrying still for 1 times.
2025-06-02 19:28:46,146 - freqtrade.exchange.common - WARNING - set_margin_mode() returned exception: "Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}". Giving up.
2025-06-02 19:28:46,149 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3530, in set_margin_mode
    res = self._api.set_margin_mode(margin_mode.value, pair, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 2655, in set_margin_mode
    response = self.privatePostExchange(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 625, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 3473, in handle_errors
    self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4881, in throw_broadly_matched_exception
    raise broad[broadKey](message)
ccxt.base.errors.InvalidOrder: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3540, in set_margin_mode
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3530, in set_margin_mode
    res = self._api.set_margin_mode(margin_mode.value, pair, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 2655, in set_margin_mode
    response = self.privatePostExchange(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 625, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 3473, in handle_errors
    self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4881, in throw_broadly_matched_exception
    raise broad[broadKey](message)
ccxt.base.errors.InvalidOrder: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3540, in set_margin_mode
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3530, in set_margin_mode
    res = self._api.set_margin_mode(margin_mode.value, pair, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 2655, in set_margin_mode
    response = self.privatePostExchange(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 625, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 3473, in handle_errors
    self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4881, in throw_broadly_matched_exception
    raise broad[broadKey](message)
ccxt.base.errors.InvalidOrder: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3540, in set_margin_mode
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3530, in set_margin_mode
    res = self._api.set_margin_mode(margin_mode.value, pair, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 2655, in set_margin_mode
    response = self.privatePostExchange(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 625, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 3473, in handle_errors
    self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4881, in throw_broadly_matched_exception
    raise broad[broadKey](message)
ccxt.base.errors.InvalidOrder: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3540, in set_margin_mode
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3530, in set_margin_mode
    res = self._api.set_margin_mode(margin_mode.value, pair, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 2655, in set_margin_mode
    response = self.privatePostExchange(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 625, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/hyperliquid.py", line 3473, in handle_errors
    self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4881, in throw_broadly_matched_exception
    raise broad[broadKey](message)
ccxt.base.errors.InvalidOrder: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.12/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1394, in _force_enter_inline
    await self._force_enter_action(pair, None, order_side)
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1374, in _force_enter_action
    await loop.run_in_executor(None, _force_enter)
  File "/usr/local/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 74, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1370, in _force_enter
    self._rpc._rpc_force_entry(pair, price, order_side=order_side)
  File "/freqtrade/freqtrade/rpc/rpc.py", line 1041, in _rpc_force_entry
    if self._freqtrade.execute_entry(
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/freqtradebot.py", line 946, in execute_entry
    order = self.exchange.create_order(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1313, in create_order
    self._lev_prep(pair, leverage, side)
  File "/freqtrade/freqtrade/exchange/hyperliquid.py", line 68, in _lev_prep
    self.set_margin_mode(pair, self.margin_mode, params={"leverage": leverage})
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 1 more time]
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 3540, in set_margin_mode
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not set margin mode due to InvalidOrder. Message: hyperliquid {"status":"err","response":"L1 error: User or API Wallet ****************************************** does not exist."}
2025-06-02 19:29:31,065 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:30:31,001 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'reload_config'}
2025-06-02 19:30:31,003 - freqtrade.worker - INFO - Changing state from RUNNING to: RELOAD_CONFIG
2025-06-02 19:30:31,008 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RELOAD_CONFIG'
2025-06-02 19:30:31,011 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:30:31,017 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:30:31,019 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-02 19:30:31,020 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-02 19:30:31,125 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-02 19:30:34,228 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-06-02 19:30:34,230 - telegram.ext.Application - INFO - Application.stop() complete
2025-06-02 19:30:35,576 - freqtrade.configuration.load_config - INFO - Using config: user_data/config.json ...
2025-06-02 19:30:35,590 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:30:35,592 - root - INFO - Logfile configured
2025-06-02 19:30:35,594 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:30:35,596 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:30:35,598 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:30:35,599 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:30:35,601 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:30:35,602 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:30:35,689 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:30:35,691 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:30:35,693 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:30:35,715 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:30:35,717 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:30:35,722 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 19:30:35,724 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 19:30:35,726 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 19:30:35,728 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 19:30:35,729 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 19:30:35,732 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 19:30:35,733 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 19:30:35,735 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 19:30:35,737 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 19:30:35,739 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 19:30:35,740 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 19:30:35,742 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 19:30:35,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 19:30:35,745 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 19:30:35,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 19:30:35,749 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 19:30:35,751 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 19:30:35,753 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 19:30:35,755 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60, 'emergency_exit': 'market'}
2025-06-02 19:30:35,757 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 19:30:35,759 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 19:30:35,761 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 19:30:35,763 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 19:30:35,764 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 19:30:35,767 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 19:30:35,768 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 19:30:35,770 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 19:30:35,772 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 19:30:35,774 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 19:30:35,776 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 19:30:35,777 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 19:30:35,779 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 19:30:35,781 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 19:30:35,783 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 19:30:35,794 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 19:30:35,796 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:30:35,814 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:30:35,835 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 19:30:39,843 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 19:30:40,431 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:30:40,432 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 19:30:40,436 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 19:30:40,687 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 19:30:40,689 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 19:30:40,691 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 19:30:40,717 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 19:30:40,751 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 19:30:40,753 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 19:30:40,760 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 19:30:40,763 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 19:30:40,768 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 19:30:40,771 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 19:30:40,773 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 19:30:40,776 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 19:30:40,777 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running after config reloaded'}
2025-06-02 19:30:40,779 - freqtrade.worker - INFO - Changing state from RELOAD_CONFIG to: RUNNING
2025-06-02 19:30:40,802 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:30:40,806 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:30:40,837 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:30:40,866 - telegram.ext.Application - INFO - Application started
2025-06-02 19:30:45,840 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:31:06,415 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:31:21,712 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:31:21,966 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 15.772200000000002 and price: 2537.6 ...
2025-06-02 19:31:25,212 - freqtrade.freqtradebot - INFO - Order 99356381407 was created for ETH/USDC:USDC and status is open.
2025-06-02 19:31:28,537 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:31:28,562 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 1.0, 'direction': 'Short', 'limit': 2537.6, 'open_rate': 2537.6, 'order_type': 'limit', 'stake_amount': 15.772200000000002, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.006215400378310215, 'open_date': datetime.datetime(2025, 6, 2, 19, 31, 25, 215700, tzinfo=datetime.timezone.utc), 'current_rate': 2537.6, 'sub_trade': False}
2025-06-02 19:31:29,832 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:31:30,887 - freqtrade.persistence.trade_model - INFO - Updating trade (id=1) ...
2025-06-02 19:31:30,890 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25).
2025-06-02 19:31:31,646 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:31:31,661 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry_fill, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 1.0, 'direction': 'Short', 'limit': 2537.6, 'open_rate': 2537.6, 'order_type': 'limit', 'stake_amount': 15.73312, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0062, 'open_date': datetime.datetime(2025, 6, 2, 19, 31, 25, 215700, tzinfo=datetime.timezone.utc), 'current_rate': 2537.6, 'sub_trade': False}
2025-06-02 19:31:31,664 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-06-02 19:31:46,931 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:32:46,936 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:33:46,941 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:34:46,945 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:35:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:36:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:37:46,011 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'reload_config'}
2025-06-02 19:37:46,013 - freqtrade.worker - INFO - Changing state from RUNNING to: RELOAD_CONFIG
2025-06-02 19:37:46,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RELOAD_CONFIG'
2025-06-02 19:37:46,021 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:37:46,034 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:37:46,036 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-02 19:37:46,038 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-02 19:37:46,188 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-02 19:37:46,273 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-06-02 19:37:46,275 - telegram.ext.Application - INFO - Application.stop() complete
2025-06-02 19:37:51,428 - freqtrade.configuration.load_config - INFO - Using config: user_data/config.json ...
2025-06-02 19:37:51,448 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:37:51,450 - root - INFO - Logfile configured
2025-06-02 19:37:51,452 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:37:51,454 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:37:51,456 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:37:51,457 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:37:51,459 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:37:51,460 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:37:51,486 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:37:51,488 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:37:51,490 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:37:51,510 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:37:51,512 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:37:51,517 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/ao_strategy.py due to 'cannot import name 'BaseExchange' from 'freqtrade.exchange' (/freqtrade/freqtrade/exchange/__init__.py)'
2025-06-02 19:37:51,600 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-06-02 19:37:51,602 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-06-02 19:37:51,603 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:37:51,614 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:37:51,616 - freqtrade - ERROR - Impossible to load Strategy 'AODualSideStrategy'. This class does not exist or contains Python code errors.
2025-06-02 19:40:59,596 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:40:59,598 - root - INFO - Logfile configured
2025-06-02 19:40:59,600 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:40:59,602 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:40:59,604 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:40:59,605 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:40:59,607 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:40:59,608 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:40:59,642 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:40:59,644 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:40:59,649 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:40:59,668 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:40:59,670 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:40:59,675 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/ao_strategy.py due to 'cannot import name 'BaseExchange' from 'freqtrade.exchange' (/freqtrade/freqtrade/exchange/__init__.py)'
2025-06-02 19:40:59,748 - freqtrade - ERROR - Impossible to load Strategy 'AODualSideStrategy'. This class does not exist or contains Python code errors.
2025-06-02 19:41:33,955 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:41:33,957 - root - INFO - Logfile configured
2025-06-02 19:41:33,959 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:41:33,961 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:41:33,963 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:41:33,965 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:41:33,966 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:41:33,969 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:41:33,999 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:41:34,001 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:41:34,005 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:41:34,025 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:41:34,027 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:41:34,033 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 19:41:34,035 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 19:41:34,038 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 19:41:34,039 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 19:41:34,041 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 19:41:34,043 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 19:41:34,044 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 19:41:34,046 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 19:41:34,048 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 19:41:34,049 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 19:41:34,051 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 19:41:34,052 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 19:41:34,054 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 19:41:34,055 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 19:41:34,057 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 19:41:34,059 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 19:41:34,060 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 19:41:34,062 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 19:41:34,063 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-06-02 19:41:34,065 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 19:41:34,067 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 19:41:34,068 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 19:41:34,070 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 19:41:34,071 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 19:41:34,073 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 19:41:34,074 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 19:41:34,076 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 19:41:34,078 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 19:41:34,079 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 19:41:34,081 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 19:41:34,083 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 19:41:34,084 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 19:41:34,086 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 19:41:34,088 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 19:41:34,124 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 19:41:34,126 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:41:34,143 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:41:34,166 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 19:41:37,403 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 19:41:38,207 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:41:38,210 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 19:41:38,859 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 19:41:39,794 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 19:41:39,796 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 19:41:39,799 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 19:41:39,886 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 19:41:39,888 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 19:41:39,894 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 19:41:39,896 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 19:41:39,902 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 19:41:39,905 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 19:41:39,907 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 19:41:39,910 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 19:41:39,912 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 19:41:39,914 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-06-02 19:41:39,936 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:41:39,938 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:41:39,974 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:41:39,980 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:41:40,082 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 19:41:40,226 - telegram.ext.Application - INFO - Application started
2025-06-02 19:41:47,578 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:42:17,582 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'stopped'}
2025-06-02 19:42:17,584 - freqtrade.worker - INFO - Changing state from RUNNING to: STOPPED
2025-06-02 19:42:17,595 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "1 open trades active.\n\nHandle these trades manually on Hyperliquid, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \n"}
2025-06-02 19:42:22,597 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-02 19:42:32,600 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 19:42:32,602 - freqtrade.worker - INFO - Changing state from STOPPED to: RUNNING
2025-06-02 19:42:32,620 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:42:32,625 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:42:32,641 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:42:32,644 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:42:39,573 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.12/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 108, in wrapper
    cchat_id: int = int(message.chat_id)
                        ^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'chat_id'
2025-06-02 19:42:39,992 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:42:49,994 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'paused'}
2025-06-02 19:42:49,997 - freqtrade.worker - INFO - Changing state from RUNNING to: PAUSED
2025-06-02 19:42:55,003 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='PAUSED'
2025-06-02 19:43:22,073 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:43:23,964 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 1, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 1.0, 'direction': 'Short', 'gain': 'profit', 'limit': 2529.8, 'order_rate': 2529.8, 'order_type': 'limit', 'amount': 0.0062, 'open_rate': 2537.6, 'close_rate': 2529.8, 'current_rate': 2529.8, 'profit_amount': 0.0436492, 'profit_ratio': 0.00277477, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 6, 2, 19, 31, 25, 215700, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 2, 19, 43, 23, 964696, tzinfo=datetime.timezone.utc), 'stake_amount': 15.73312, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-06-02 19:43:24,489 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:43:25,329 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:30,446 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:35,286 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:40,277 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:45,007 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 19:43:45,009 - freqtrade.worker - INFO - Changing state from PAUSED to: RUNNING
2025-06-02 19:43:45,294 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:50,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:43:50,285 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:43:54,177 - freqtrade.rpc.telegram - ERROR - Forcebuy error!
Traceback (most recent call last):
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1374, in _force_enter_action
    await loop.run_in_executor(None, _force_enter)
  File "/usr/local/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 74, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1370, in _force_enter
    self._rpc._rpc_force_entry(pair, price, order_side=order_side)
  File "/freqtrade/freqtrade/rpc/rpc.py", line 1019, in _rpc_force_entry
    raise RPCException(f"position for {pair} already open - id: {trade.id}")
freqtrade.rpc.rpc.RPCException: position for ETH/USDC:USDC already open - id: 1
2025-06-02 19:43:55,324 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:44:01,351 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25)
2025-06-02 19:44:02,372 - freqtrade.persistence.trade_model - INFO - Updating trade (id=1) ...
2025-06-02 19:44:02,374 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=2025-06-02 19:31:25).
2025-06-02 19:44:02,377 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=1, pair=ETH/USDC:USDC, amount=0.00620000, is_short=True, leverage=1.0, open_rate=2537.60000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-06-02 19:44:02,884 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:44:02,892 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 1, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 1.0, 'direction': 'Short', 'gain': 'profit', 'limit': 2529.8, 'order_rate': 2529.8, 'order_type': 'limit', 'amount': 0.0062, 'open_rate': 2537.6, 'close_rate': 2529.8, 'current_rate': None, 'profit_amount': 0.04364905, 'profit_ratio': 0.00277476, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 6, 2, 19, 31, 25, 215700, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 2, 19, 44, 1, 353000, tzinfo=datetime.timezone.utc), 'stake_amount': 15.73312, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.04364905, 'final_profit_ratio': 0.0027747576907836626, 'is_final_exit': True}
2025-06-02 19:44:23,768 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:44:24,036 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 15.81453953 and price: 2530.4 ...
2025-06-02 19:44:27,253 - freqtrade.freqtradebot - INFO - Order 99358878666 was created for ETH/USDC:USDC and status is open.
2025-06-02 19:44:30,407 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:44:30,418 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'limit': 2530.4, 'open_rate': 2530.4, 'order_type': 'limit', 'stake_amount': 15.81453953, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.03124909012409105, 'open_date': datetime.datetime(2025, 6, 2, 19, 44, 27, 255679, tzinfo=datetime.timezone.utc), 'current_rate': 2530.4, 'sub_trade': False}
2025-06-02 19:44:30,781 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:44:31,080 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:44:36,090 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:44:41,081 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:44:47,078 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:44:48,079 - freqtrade.persistence.trade_model - INFO - Updating trade (id=2) ...
2025-06-02 19:44:48,082 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27).
2025-06-02 19:44:48,885 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:44:48,901 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry_fill, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'limit': 2530.4000000000005, 'open_rate': 2530.4000000000005, 'order_type': 'limit', 'stake_amount': 15.789696000000003, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0312, 'open_date': datetime.datetime(2025, 6, 2, 19, 44, 27, 255679, tzinfo=datetime.timezone.utc), 'current_rate': 2530.4, 'sub_trade': False}
2025-06-02 19:44:48,904 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-06-02 19:44:50,808 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:45:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:46:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:47:01,010 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'reload_config'}
2025-06-02 19:47:01,012 - freqtrade.worker - INFO - Changing state from RUNNING to: RELOAD_CONFIG
2025-06-02 19:47:01,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RELOAD_CONFIG'
2025-06-02 19:47:01,020 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:47:01,032 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:47:01,034 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-02 19:47:01,036 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-02 19:47:01,206 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-02 19:47:04,297 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-06-02 19:47:04,299 - telegram.ext.Application - INFO - Application.stop() complete
2025-06-02 19:47:10,693 - freqtrade.configuration.load_config - INFO - Using config: user_data/config.json ...
2025-06-02 19:47:10,708 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:47:10,710 - root - INFO - Logfile configured
2025-06-02 19:47:10,712 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:47:10,714 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:47:10,715 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:47:10,717 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:47:10,719 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:47:10,720 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:47:10,745 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:47:10,748 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:47:10,750 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:47:10,770 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:47:10,772 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:47:10,777 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 19:47:10,779 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 19:47:10,782 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 19:47:10,784 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 19:47:10,785 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 19:47:10,787 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 19:47:10,789 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 19:47:10,790 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 19:47:10,792 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 19:47:10,794 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 19:47:10,795 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 19:47:10,797 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 19:47:10,798 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 19:47:10,800 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 19:47:10,801 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 19:47:10,803 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 19:47:10,805 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 19:47:10,806 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 19:47:10,808 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-02 19:47:10,810 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 19:47:10,811 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 19:47:10,813 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 19:47:10,815 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 19:47:10,817 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 19:47:10,819 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 19:47:10,820 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 19:47:10,822 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 19:47:10,824 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 19:47:10,825 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 19:47:10,827 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 19:47:10,828 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 19:47:10,830 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 19:47:10,832 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 19:47:10,833 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-06-02 19:47:10,835 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-06-02 19:47:10,837 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-02 19:47:10,846 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-02 19:47:10,849 - freqtrade - ERROR - Configuration error: Market entry orders require entry_pricing.price_side = "other".
Please make sure to review the documentation at https://www.freqtrade.io/en/stable.
2025-06-02 19:49:08,092 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 19:49:08,094 - root - INFO - Logfile configured
2025-06-02 19:49:08,097 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 19:49:08,099 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-02 19:49:08,101 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-02 19:49:08,102 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-02 19:49:08,104 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-02 19:49:08,106 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-02 19:49:08,143 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 19:49:08,146 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-02 19:49:08,150 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 19:49:08,171 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-02 19:49:08,173 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 19:49:08,180 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-02 19:49:08,182 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 19:49:08,186 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 19:49:08,188 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-02 19:49:08,189 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-02 19:49:08,191 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-02 19:49:08,194 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-02 19:49:08,196 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-02 19:49:08,198 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-02 19:49:08,200 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-02 19:49:08,202 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-02 19:49:08,203 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 19:49:08,205 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-02 19:49:08,206 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-02 19:49:08,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-02 19:49:08,210 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 19:49:08,211 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 19:49:08,213 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 19:49:08,215 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-02 19:49:08,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 19:49:08,219 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-02 19:49:08,221 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-02 19:49:08,222 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-02 19:49:08,224 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 19:49:08,226 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 19:49:08,228 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 19:49:08,229 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-02 19:49:08,231 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 19:49:08,233 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 19:49:08,234 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 19:49:08,236 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 19:49:08,237 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 19:49:08,239 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-02 19:49:08,241 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 19:49:08,279 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-02 19:49:08,281 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:49:08,298 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-02 19:49:08,318 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-02 19:49:12,271 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-02 19:49:13,260 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 19:49:13,262 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-06-02 19:49:13,828 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-06-02 19:49:14,742 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-06-02 19:49:14,746 - freqtrade.rpc.api_server.webserver - WARNING - SECURITY WARNING - No password for local REST Server defined. Please make sure that this is intentional!
2025-06-02 19:49:14,748 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-06-02 19:49:14,840 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 19:49:14,842 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-02 19:49:14,849 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 19:49:14,851 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.01s
2025-06-02 19:49:14,856 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 19:49:14,858 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 19:49:14,860 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 19:49:14,863 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-02 19:49:14,865 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 19:49:14,866 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-06-02 19:49:14,889 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 19:49:14,891 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 19:49:14,928 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 19:49:14,935 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 19:49:15,055 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-06-02 19:49:15,214 - telegram.ext.Application - INFO - Application started
2025-06-02 19:49:22,281 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:50:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:51:26,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:52:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:53:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:54:26,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:55:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:56:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:57:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:58:31,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 19:59:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:00:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:01:09,183 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 20:01:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:02:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:03:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:04:36,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:05:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:06:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:07:41,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:08:41,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:09:41,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:10:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:11:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:12:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:13:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:14:46,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:15:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:16:51,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:17:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:18:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:19:51,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:20:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:21:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:22:56,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:23:56,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:24:56,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:26:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:27:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:28:01,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:29:01,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:30:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:31:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:31:09,229 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 20:32:06,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:33:06,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:34:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:35:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:36:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:37:11,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:38:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:39:11,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:40:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:41:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:42:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:43:16,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:44:16,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:45:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:46:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:47:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:48:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:49:20,851 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 20:49:21,117 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:50:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:51:26,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:52:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:53:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:54:26,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:55:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:56:31,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:57:31,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:58:31,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 20:59:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:00:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:01:09,194 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:01:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:02:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:03:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:04:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:05:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:06:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:07:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:08:41,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:09:41,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:10:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:11:46,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:12:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:13:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:14:46,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:14:56,023 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'paused'}
2025-06-02 21:14:56,026 - freqtrade.worker - INFO - Changing state from RUNNING to: PAUSED
2025-06-02 21:15:01,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='PAUSED'
2025-06-02 21:15:01,002 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'stopped'}
2025-06-02 21:15:01,004 - freqtrade.worker - INFO - Changing state from PAUSED to: STOPPED
2025-06-02 21:15:01,014 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "1 open trades active.\n\nHandle these trades manually on Hyperliquid, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \n"}
2025-06-02 21:15:06,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-02 21:15:44,084 - freqtrade.rpc.telegram - ERROR - Exception occurred within Telegram module
Traceback (most recent call last):
  File "/freqtrade/freqtrade/rpc/telegram.py", line 130, in wrapper
    return await command_handler(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 1501, in _cancel_open_order
    trade_id = int(context.args[0])
               ^^^^^^^^^^^^^^^^^^^^
ValueError: invalid literal for int() with base 10: 'all'
2025-06-02 21:16:06,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-02 21:16:06,023 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-06-02 21:16:06,025 - freqtrade.worker - INFO - Changing state from STOPPED to: RUNNING
2025-06-02 21:16:06,043 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `hyperliquid`\n*Stake per trade:* `unlimited USDC`\n*Minimum ROI:* `{'0': 3.0}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `AODualSideStrategy`"}
2025-06-02 21:16:06,045 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDC pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-06-02 21:16:06,059 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 21:16:06,064 - freqtrade.freqtradebot - INFO - Updating 0 open orders.
2025-06-02 21:16:11,403 - freqtrade.freqtradebot - INFO - Exit for ETH/USDC:USDC detected. Reason: exit_signal Tag: ao_cross_up_exit
2025-06-02 21:16:14,269 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:16,019 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 2, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'gain': 'loss', 'limit': 2548.1, 'order_rate': 2548.1, 'order_type': 'market', 'amount': 0.0312, 'open_rate': 2530.4, 'close_rate': 2548.1, 'current_rate': 2548.1, 'profit_amount': -0.57600421, 'profit_ratio': -0.03648523, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exit_reason': 'ao_cross_up_exit', 'open_date': datetime.datetime(2025, 6, 2, 19, 44, 27, 255679, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 2, 21, 16, 16, 19326, tzinfo=datetime.timezone.utc), 'stake_amount': 15.789696000000003, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-06-02 21:16:16,537 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:16,542 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:16:17,945 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27)
2025-06-02 21:16:19,413 - freqtrade.persistence.trade_model - INFO - Updating trade (id=2) ...
2025-06-02 21:16:19,415 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=2025-06-02 19:44:27).
2025-06-02 21:16:19,418 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=2, pair=ETH/USDC:USDC, amount=0.03120000, is_short=True, leverage=5.0, open_rate=2530.40000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-06-02 21:16:19,926 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:19,934 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 2, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'gain': 'loss', 'limit': 2548.1, 'order_rate': 2548.1, 'order_type': 'limit', 'amount': 0.0312, 'open_rate': 2530.4, 'close_rate': 2548.1, 'current_rate': None, 'profit_amount': -0.59985602, 'profit_ratio': -0.03799604, 'buy_tag': 'force_entry', 'enter_tag': 'force_entry', 'exit_reason': 'ao_cross_up_exit', 'open_date': datetime.datetime(2025, 6, 2, 19, 44, 27, 255679, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 2, 21, 16, 17, 947000, tzinfo=datetime.timezone.utc), 'stake_amount': 15.789696000000003, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': -0.59985602, 'final_profit_ratio': -0.037996045009420394, 'is_final_exit': True}
2025-06-02 21:16:19,966 - freqtrade.rpc.rpc - WARNING - force_exit: Invalid argument received
2025-06-02 21:16:20,825 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:21,082 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 15.235262319999999 and price: 2548.1 ...
2025-06-02 21:16:24,000 - freqtrade.freqtradebot - INFO - Order 99375741846 was created for ETH/USDC:USDC and status is open.
2025-06-02 21:16:26,881 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:26,889 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'limit': 2548.7, 'open_rate': 2548.7, 'order_type': 'market', 'stake_amount': 15.235262319999999, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0298, 'open_date': datetime.datetime(2025, 6, 2, 21, 16, 24, 2448, tzinfo=datetime.timezone.utc), 'current_rate': 2548.1, 'sub_trade': False}
2025-06-02 21:16:28,271 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ETH/USDC:USDC, amount=0.00000000, is_short=False, leverage=5.0, open_rate=2548.10000000, open_since=2025-06-02 21:16:24)
2025-06-02 21:16:29,315 - freqtrade.persistence.trade_model - INFO - Updating trade (id=3) ...
2025-06-02 21:16:29,317 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=3, pair=ETH/USDC:USDC, amount=0.02980000, is_short=False, leverage=5.0, open_rate=2548.70000000, open_since=2025-06-02 21:16:24).
2025-06-02 21:16:30,082 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:16:30,096 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry_fill, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'limit': 2548.7, 'open_rate': 2548.7, 'order_type': 'limit', 'stake_amount': 15.190252000000001, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0298, 'open_date': datetime.datetime(2025, 6, 2, 21, 16, 24, 2448, tzinfo=datetime.timezone.utc), 'current_rate': 2548.1, 'sub_trade': False}
2025-06-02 21:16:30,099 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-06-02 21:17:16,898 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:18:16,903 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:19:16,907 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:22:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:23:21,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:24:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:26:26,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:27:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:28:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:29:26,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:30:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:31:09,208 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 21:31:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:32:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:33:31,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:34:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:36:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:37:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:38:36,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:39:36,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:41:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:42:41,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:43:41,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:44:41,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:46:46,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:47:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:48:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:49:25,868 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 21:49:46,141 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:50:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:51:51,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:52:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:53:51,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:54:51,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:55:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:56:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:57:56,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:58:56,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 21:59:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:01:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:01:09,178 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 22:02:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:03:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:04:01,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:05:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:06:06,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:07:06,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:08:06,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:09:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:10:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:11:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:12:11,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:13:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:14:11,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:16:16,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:17:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:18:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:19:16,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:22:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:23:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:24:21,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:26:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:27:26,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:28:26,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:29:26,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:30:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:31:09,432 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 22:31:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:32:31,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:33:31,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:34:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:36:36,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:37:36,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:38:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:39:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:41:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:42:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:43:41,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:44:41,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:46:46,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:47:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:48:46,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:49:30,981 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 22:49:46,248 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:50:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:51:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:52:51,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:53:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:54:51,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:55:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:56:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:57:56,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:58:56,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 22:59:56,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:01:01,003 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:01:09,108 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 23:02:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:03:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:04:01,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:05:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:06:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:07:06,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:08:06,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:09:06,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:10:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:11:11,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:12:11,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:13:11,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:14:11,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:16:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:17:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:18:16,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:19:16,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:22:21,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:23:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:24:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:26:26,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:27:26,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:28:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:29:26,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:30:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:31:09,191 - freqtrade.wallets - INFO - Wallets synced.
2025-06-02 23:31:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:32:31,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:33:31,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:34:31,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:36:36,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:37:36,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:38:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:39:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:41:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:42:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:43:41,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:44:41,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:46:46,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:47:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:48:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:49:35,878 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-02 23:49:46,146 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:50:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:51:51,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:52:51,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:53:51,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:54:51,028 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:55:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:56:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:57:56,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:58:56,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-02 23:59:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:01:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:01:09,213 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 00:02:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:03:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:04:01,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:05:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:06:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:07:06,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:08:06,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:09:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:10:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:11:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:12:11,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:13:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:14:11,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:16:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:17:16,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:18:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:19:16,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:22:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:23:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:24:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:26:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:27:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:28:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:29:26,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:30:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:31:09,266 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 00:31:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:32:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:33:31,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:34:31,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:36:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:37:36,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:38:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:39:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:41:41,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:42:41,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:43:41,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:44:41,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:46:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:47:46,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:48:46,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:49:41,210 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 00:49:46,485 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:50:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:51:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:52:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:53:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:54:51,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:55:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:56:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:57:56,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:58:56,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 00:59:56,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:01:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:01:09,322 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 01:02:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:03:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:04:01,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:05:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:06:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:07:06,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:08:06,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:09:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:10:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:11:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:12:11,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:13:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:14:11,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:16:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:17:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:18:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:19:16,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:22:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:23:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:24:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:26:26,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:27:26,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:28:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:29:26,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:30:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:31:09,475 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 01:31:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:32:31,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:33:31,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:34:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:36:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:37:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:38:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:39:36,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:41:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:42:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:43:41,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:44:41,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:46:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:47:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:48:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:49:46,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:49:50,717 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 01:50:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:51:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:52:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:53:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:54:51,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:55:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:56:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:57:56,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:58:56,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 01:59:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:01:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:01:09,071 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:02:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:03:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:04:01,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:05:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:06:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:07:06,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:08:06,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:09:06,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:10:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:11:11,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:12:11,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:13:11,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:14:11,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:16:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:17:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:18:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:19:16,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:21:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:22:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:23:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:24:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:25:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:26:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:27:26,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:28:26,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:29:26,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:30:31,004 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:31:09,145 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:31:31,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:32:31,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:33:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:34:31,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:35:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:36:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:37:36,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:38:36,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:39:36,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:40:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:41:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:42:41,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:43:41,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:44:41,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:45:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:46:46,008 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:47:46,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:48:46,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:49:46,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:49:55,936 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 02:50:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:51:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:52:51,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:53:51,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:54:51,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:55:04,427 - freqtrade.freqtradebot - INFO - Exit for ETH/USDC:USDC detected. Reason: exit_signal Tag: ao_cross_down_exit
2025-06-03 02:55:07,579 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:09,603 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 3, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'gain': 'profit', 'limit': 2619.4, 'order_rate': 2619.4, 'order_type': 'market', 'amount': 0.0298, 'open_rate': 2548.7, 'close_rate': 2619.4, 'current_rate': 2619.4, 'profit_amount': 2.03417929, 'profit_ratio': 0.13385323, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exit_reason': 'ao_cross_down_exit', 'open_date': datetime.datetime(2025, 6, 2, 21, 16, 24, 2448, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 3, 2, 55, 9, 603314, tzinfo=datetime.timezone.utc), 'stake_amount': 15.190252000000001, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-06-03 02:55:10,134 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:11,460 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ETH/USDC:USDC, amount=0.02980000, is_short=False, leverage=5.0, open_rate=2548.70000000, open_since=2025-06-02 21:16:24)
2025-06-03 02:55:12,461 - freqtrade.persistence.trade_model - INFO - Updating trade (id=3) ...
2025-06-03 02:55:12,463 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=3, pair=ETH/USDC:USDC, amount=0.02980000, is_short=False, leverage=5.0, open_rate=2548.70000000, open_since=2025-06-02 21:16:24).
2025-06-03 02:55:12,467 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=3, pair=ETH/USDC:USDC, amount=0.02980000, is_short=False, leverage=5.0, open_rate=2548.70000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-06-03 02:55:13,382 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:13,390 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 3, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'gain': 'profit', 'limit': 2619.4, 'order_rate': 2619.4, 'order_type': 'limit', 'amount': 0.0298, 'open_rate': 2548.7, 'close_rate': 2619.4, 'current_rate': None, 'profit_amount': 2.03417929, 'profit_ratio': 0.13385323, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exit_reason': 'ao_cross_down_exit', 'open_date': datetime.datetime(2025, 6, 2, 21, 16, 24, 2448, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 3, 2, 55, 11, 462000, tzinfo=datetime.timezone.utc), 'stake_amount': 15.190252000000001, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 2.03417929, 'final_profit_ratio': 0.1338532317341098, 'is_final_exit': True}
2025-06-03 02:55:14,212 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:14,503 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 17.19887891 and price: 2619.4 ...
2025-06-03 02:55:17,597 - freqtrade.freqtradebot - INFO - Order 99445134422 was created for ETH/USDC:USDC and status is open.
2025-06-03 02:55:20,612 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:20,623 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 4, 'type': entry, 'buy_tag': 'ao_cross_down', 'enter_tag': 'ao_cross_down', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'limit': 2619.4, 'open_rate': 2619.4, 'order_type': 'market', 'stake_amount': 17.19887891, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0328, 'open_date': datetime.datetime(2025, 6, 3, 2, 55, 17, 599613, tzinfo=datetime.timezone.utc), 'current_rate': 2619.4, 'sub_trade': False}
2025-06-03 02:55:21,909 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=4, pair=ETH/USDC:USDC, amount=0.00000000, is_short=True, leverage=5.0, open_rate=2619.40000000, open_since=2025-06-03 02:55:17)
2025-06-03 02:55:22,978 - freqtrade.persistence.trade_model - INFO - Updating trade (id=4) ...
2025-06-03 02:55:22,982 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=4, pair=ETH/USDC:USDC, amount=0.03280000, is_short=True, leverage=5.0, open_rate=2619.40000000, open_since=2025-06-03 02:55:17).
2025-06-03 02:55:23,863 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 02:55:23,876 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 4, 'type': entry_fill, 'buy_tag': 'ao_cross_down', 'enter_tag': 'ao_cross_down', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'limit': 2619.4, 'open_rate': 2619.4, 'order_type': 'limit', 'stake_amount': 17.183264, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0328, 'open_date': datetime.datetime(2025, 6, 3, 2, 55, 17, 599613, tzinfo=datetime.timezone.utc), 'current_rate': 2619.4, 'sub_trade': False}
2025-06-03 02:55:23,879 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-06-03 02:55:55,635 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:56:55,640 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:57:55,645 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:58:55,650 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 02:59:55,656 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:00:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:01:09,652 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 03:01:56,008 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:02:56,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:03:56,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:04:56,031 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:06:01,003 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:07:01,008 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:08:01,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:09:01,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:10:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:11:06,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:12:06,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:13:06,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:14:06,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:15:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:16:11,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:17:11,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:18:11,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:19:11,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:20:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:21:16,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:22:16,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:23:16,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:24:16,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:25:21,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:26:21,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:27:21,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:28:21,030 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:29:21,035 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:30:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:31:09,244 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 03:31:26,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:32:26,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:33:26,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:34:26,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:35:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:36:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:37:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:38:31,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:39:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:40:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:41:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:42:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:43:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:44:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:45:41,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:46:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:47:41,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:48:41,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:49:41,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:50:00,626 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 03:50:43,421 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:51:43,427 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:52:43,432 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:53:43,437 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:54:43,443 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:55:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:56:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:57:46,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:58:46,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 03:59:46,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:00:12,067 - freqtrade.exchange.common - WARNING - _async_get_candle_history() returned exception: "Could not fetch historical candle (OHLCV) data for ETH/USDC:USDC, 5m, futures due to RequestTimeout. Message: hyperliquid POST https://api.hyperliquid.xyz/info". Retrying still for 4 times.
2025-06-03 04:00:24,817 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to RequestTimeout. Message: hyperliquid POST https://api.hyperliquid.xyz/info". Retrying still for 4 times.
2025-06-03 04:00:50,103 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:01:08,313 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 04:01:50,109 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:02:50,114 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:03:50,119 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:04:50,124 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:05:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:06:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:07:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:08:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:09:51,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:10:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:11:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:12:56,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:13:56,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:14:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:16:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:17:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:18:01,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:19:01,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:20:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:21:06,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:22:06,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:23:06,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:24:06,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:25:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:26:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:27:11,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:28:11,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:29:11,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:30:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:31:09,144 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 04:31:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:32:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:33:16,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:34:16,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:35:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:36:21,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:37:21,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:38:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:39:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:40:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:41:26,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:42:26,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:43:26,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:44:26,024 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:45:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:46:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:47:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:48:31,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:49:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:50:05,938 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 04:50:33,877 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:51:33,882 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:52:33,887 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:53:33,892 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:54:33,896 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:55:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:56:36,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:57:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:58:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 04:59:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:00:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:01:09,248 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:01:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:02:41,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:03:41,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:04:41,025 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:05:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:06:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:07:46,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:08:46,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:09:46,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:10:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:11:51,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:12:51,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:13:51,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:14:51,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:15:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:16:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:17:56,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:18:56,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:19:56,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:21:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:22:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:23:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:24:01,017 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:25:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:26:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:27:06,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:28:06,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:29:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:30:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:31:09,287 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:31:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:32:11,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:33:11,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:34:11,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:35:04,052 - freqtrade.freqtradebot - INFO - Exit for ETH/USDC:USDC detected. Reason: exit_signal Tag: ao_cross_up_exit
2025-06-03 05:35:07,029 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:09,042 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 4, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'gain': 'profit', 'limit': 2607.2, 'order_rate': 2607.2, 'order_type': 'market', 'amount': 0.0328, 'open_rate': 2619.4, 'close_rate': 2607.2, 'current_rate': 2607.2, 'profit_amount': 0.32301538, 'profit_ratio': 0.01880672, 'buy_tag': 'ao_cross_down', 'enter_tag': 'ao_cross_down', 'exit_reason': 'ao_cross_up_exit', 'open_date': datetime.datetime(2025, 6, 3, 2, 55, 17, 599613, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 3, 5, 35, 9, 42792, tzinfo=datetime.timezone.utc), 'stake_amount': 17.183264, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-06-03 05:35:09,695 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:10,990 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=4, pair=ETH/USDC:USDC, amount=0.03280000, is_short=True, leverage=5.0, open_rate=2619.40000000, open_since=2025-06-03 02:55:17)
2025-06-03 05:35:11,993 - freqtrade.persistence.trade_model - INFO - Updating trade (id=4) ...
2025-06-03 05:35:11,995 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=4, pair=ETH/USDC:USDC, amount=0.03280000, is_short=True, leverage=5.0, open_rate=2619.40000000, open_since=2025-06-03 02:55:17).
2025-06-03 05:35:11,998 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=4, pair=ETH/USDC:USDC, amount=0.03280000, is_short=True, leverage=5.0, open_rate=2619.40000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-06-03 05:35:12,567 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:12,575 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 4, 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Short', 'gain': 'profit', 'limit': 2607.2, 'order_rate': 2607.2, 'order_type': 'limit', 'amount': 0.0328, 'open_rate': 2619.4, 'close_rate': 2607.2, 'current_rate': None, 'profit_amount': 0.32301538, 'profit_ratio': 0.01880672, 'buy_tag': 'ao_cross_down', 'enter_tag': 'ao_cross_down', 'exit_reason': 'ao_cross_up_exit', 'open_date': datetime.datetime(2025, 6, 3, 2, 55, 17, 599613, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 6, 3, 5, 35, 10, 994000, tzinfo=datetime.timezone.utc), 'stake_amount': 17.183264, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'sub_trade': False, 'cumulative_profit': 0.32301538, 'final_profit_ratio': 0.01880671811601466, 'is_final_exit': True}
2025-06-03 05:35:13,322 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:13,805 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for ETH/USDC:USDC with stake_amount: 17.51645109 and price: 2607.3 ...
2025-06-03 05:35:17,191 - freqtrade.freqtradebot - INFO - Order 99471013645 was created for ETH/USDC:USDC and status is open.
2025-06-03 05:35:20,266 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:20,274 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 5, 'type': entry, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'limit': 2607.3, 'open_rate': 2607.3, 'order_type': 'market', 'stake_amount': 17.51645109, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0335, 'open_date': datetime.datetime(2025, 6, 3, 5, 35, 17, 193303, tzinfo=datetime.timezone.utc), 'current_rate': 2607.3, 'sub_trade': False}
2025-06-03 05:35:20,283 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:35:21,557 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=5, pair=ETH/USDC:USDC, amount=0.00000000, is_short=False, leverage=5.0, open_rate=2607.30000000, open_since=2025-06-03 05:35:17)
2025-06-03 05:35:22,787 - freqtrade.persistence.trade_model - INFO - Updating trade (id=5) ...
2025-06-03 05:35:22,789 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=5, pair=ETH/USDC:USDC, amount=0.03350000, is_short=False, leverage=5.0, open_rate=2607.30000000, open_since=2025-06-03 05:35:17).
2025-06-03 05:35:23,784 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 05:35:23,797 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 5, 'type': entry_fill, 'buy_tag': 'ao_cross_up', 'enter_tag': 'ao_cross_up', 'exchange': 'Hyperliquid', 'pair': 'ETH/USDC:USDC', 'leverage': 5.0, 'direction': 'Long', 'limit': 2607.3, 'open_rate': 2607.3, 'order_type': 'limit', 'stake_amount': 17.46891, 'stake_currency': 'USDC', 'base_currency': 'ETH', 'quote_currency': 'USDC', 'fiat_currency': '', 'amount': 0.0335, 'open_date': datetime.datetime(2025, 6, 3, 5, 35, 17, 193303, tzinfo=datetime.timezone.utc), 'current_rate': 2607.3, 'sub_trade': False}
2025-06-03 05:35:23,800 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-06-03 05:36:20,288 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:37:20,293 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:38:20,297 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:39:20,302 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:40:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:41:21,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:42:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:43:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:44:21,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:45:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:46:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:47:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:48:26,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:49:26,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:50:11,142 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 05:50:26,439 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:51:26,445 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:52:26,449 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:53:26,454 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:54:26,459 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:55:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:56:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:57:31,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:58:31,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 05:59:31,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:00:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:01:09,096 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 06:01:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:02:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:03:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:04:36,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:05:41,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:06:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:07:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:08:41,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:09:41,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:10:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:11:46,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:12:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:13:46,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:14:46,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:15:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:16:51,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:17:51,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:18:51,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:19:51,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:20:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:21:56,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:22:56,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:23:56,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:24:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:26:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:27:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:28:01,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:29:01,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:30:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:31:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:31:09,313 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 06:32:06,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:33:06,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:34:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:35:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:36:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:37:11,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:38:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:39:11,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:40:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:41:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:42:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:43:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:44:16,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:45:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:46:21,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:47:21,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:48:21,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:49:21,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:50:20,868 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 06:50:21,140 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:51:21,145 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:52:21,149 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:53:21,154 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:54:21,159 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:55:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:56:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:57:26,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:58:26,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 06:59:26,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:00:31,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:01:09,272 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 07:01:31,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:02:31,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:03:31,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:04:31,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:05:36,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:06:36,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:07:36,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:08:36,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:09:36,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:10:41,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:11:41,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:12:41,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:13:41,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:14:41,020 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:15:46,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:16:46,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:17:46,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:18:46,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:19:46,021 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:20:51,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:21:51,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:22:51,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:23:51,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:24:51,023 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:25:56,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:26:56,008 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:27:56,013 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:28:56,018 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:29:56,022 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:31:01,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:31:09,125 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 07:32:01,007 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:33:01,011 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:34:01,016 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:35:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:36:06,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:37:06,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:38:06,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:39:06,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:40:11,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:41:11,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:42:11,009 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:43:11,014 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:44:11,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:45:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:46:16,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:47:16,010 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:48:16,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:49:16,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:50:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:50:25,870 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 1 pairs: ['ETH/USDC:USDC']
2025-06-03 07:51:21,138 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:52:21,142 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:53:21,147 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:54:21,152 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:55:26,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:56:26,006 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:57:26,012 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='RUNNING'
2025-06-03 07:57:59,367 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-06-03 07:57:59,373 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-06-03 07:57:59,387 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-03 07:57:59,403 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "1 open trades active.\n\nHandle these trades manually on Hyperliquid, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \n"}
2025-06-03 07:57:59,407 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-03 07:57:59,413 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-06-03 07:57:59,416 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-06-03 07:57:59,618 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-06-03 07:57:59,787 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-06-03 07:57:59,790 - telegram.ext.Application - INFO - Application.stop() complete
2025-06-03 07:58:02,165 - freqtrade - INFO - SIGINT received, aborting ...
2025-06-03 10:33:15,036 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:33:15,038 - root - INFO - Logfile configured
2025-06-03 10:33:15,040 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:33:15,043 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:33:15,044 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:33:15,046 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:33:15,048 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:33:15,050 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:33:15,094 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:33:15,097 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-03 10:33:15,101 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:33:15,125 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-03 10:33:15,127 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:33:15,134 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:33:15,136 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:33:15,139 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:33:15,142 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:33:15,144 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:33:15,146 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:33:15,149 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:33:15,151 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:33:15,153 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:33:15,156 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:33:15,157 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:33:15,159 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:33:15,161 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:33:15,163 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:33:15,165 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:33:15,166 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:33:15,168 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:33:15,170 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:33:15,172 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:33:15,174 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:33:15,175 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:33:15,177 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:33:15,179 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:33:15,180 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:33:15,182 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:33:15,184 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:33:15,186 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:33:15,188 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:33:15,190 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:33:15,191 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:33:15,193 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:33:15,195 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:33:15,197 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:33:15,199 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:33:15,237 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:33:15,239 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:33:15,257 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:33:15,277 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-03 10:33:19,238 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-03 10:33:19,368 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-03 10:33:19,370 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-03 10:33:19,372 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-03 10:33:19,375 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-03 10:33:19,377 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-03 10:33:19,380 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-03 10:35:43,262 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:35:43,264 - root - INFO - Logfile configured
2025-06-03 10:35:43,266 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:35:43,268 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:35:43,270 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:35:43,272 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:35:43,274 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:35:43,275 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:35:43,304 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:35:43,306 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/hyperliquid ...
2025-06-03 10:35:43,310 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:35:43,329 - freqtrade.exchange.check_exchange - INFO - Exchange "hyperliquid" is officially supported by the Freqtrade development team.
2025-06-03 10:35:43,332 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:35:43,338 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:35:43,340 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:35:43,342 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:35:43,344 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:35:43,346 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:35:43,347 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:35:43,349 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:35:43,351 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:35:43,352 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:35:43,354 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:35:43,356 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:35:43,357 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:35:43,359 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:35:43,360 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:35:43,362 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:35:43,364 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:35:43,365 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:35:43,367 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:35:43,369 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:35:43,371 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:35:43,372 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:35:43,374 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:35:43,376 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:35:43,377 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:35:43,379 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:35:43,381 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:35:43,383 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:35:43,384 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:35:43,386 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:35:43,388 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:35:43,389 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:35:43,391 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:35:43,393 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:35:43,394 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:35:43,431 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:35:43,433 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:35:43,451 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:35:43,470 - freqtrade.exchange.exchange - INFO - Using Exchange "Hyperliquid"
2025-06-03 10:35:47,506 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Hyperliquid'...
2025-06-03 10:35:47,614 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 4 times.
2025-06-03 10:35:47,616 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 3 times.
2025-06-03 10:35:47,619 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 2 times.
2025-06-03 10:35:47,621 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Retrying still for 1 times.
2025-06-03 10:35:47,623 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set". Giving up.
2025-06-03 10:35:47,625 - freqtrade - ERROR - Could not get balance due to ArgumentsRequired. Message: hyperliquid fetchBalance() requires a user parameter inside 'params' or the wallet address set
2025-06-03 10:36:41,301 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:36:41,303 - root - INFO - Logfile configured
2025-06-03 10:36:41,305 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:36:41,307 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:36:41,309 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:36:41,310 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:36:41,312 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:36:41,314 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:36:41,343 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:36:41,345 - freqtrade.configuration.directory_operations - INFO - Created data directory: None
2025-06-03 10:36:41,347 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:36:41,352 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:36:41,405 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:36:41,407 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:36:41,414 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:36:41,416 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:36:41,418 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:36:41,420 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:36:41,421 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:36:41,423 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:36:41,425 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:36:41,427 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:36:41,428 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:36:41,430 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:36:41,432 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:36:41,433 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:36:41,435 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:36:41,436 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:36:41,438 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:36:41,440 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:36:41,441 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:36:41,443 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:36:41,444 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:36:41,446 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:36:41,448 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:36:41,449 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:36:41,451 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:36:41,452 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:36:41,454 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:36:41,456 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:36:41,458 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:36:41,459 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:36:41,461 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:36:41,463 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:36:41,464 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:36:41,466 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:36:41,467 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:36:41,469 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:36:41,481 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:36:41,483 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:36:41,556 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:36:41,608 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-03 10:36:47,200 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential". Retrying still for 4 times.
2025-06-03 10:36:47,250 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential". Retrying still for 3 times.
2025-06-03 10:36:47,300 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential". Retrying still for 2 times.
2025-06-03 10:36:47,350 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential". Retrying still for 1 times.
2025-06-03 10:36:47,401 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential". Giving up.
2025-06-03 10:36:47,403 - freqtrade - ERROR - Could not load leverage tiers due to AuthenticationError. Message: binance requires "secret" credential
2025-06-03 10:36:47,756 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:36:47,759 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:39:15,067 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:39:15,069 - root - INFO - Logfile configured
2025-06-03 10:39:15,071 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:39:15,073 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:39:15,074 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:39:15,076 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:39:15,077 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:39:15,079 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:39:15,114 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:39:15,117 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:39:15,121 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:39:15,170 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:39:15,172 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:39:15,178 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:39:15,180 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:39:15,182 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:39:15,184 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:39:15,186 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:39:15,187 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:39:15,189 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:39:15,191 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:39:15,192 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:39:15,194 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:39:15,196 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:39:15,197 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:39:15,199 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:39:15,200 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:39:15,202 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:39:15,203 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:39:15,205 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:39:15,207 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:39:15,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:39:15,210 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:39:15,211 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:39:15,213 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:39:15,214 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:39:15,216 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:39:15,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:39:15,219 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:39:15,221 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:39:15,222 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:39:15,224 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:39:15,225 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:39:15,227 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:39:15,230 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:39:15,231 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:39:15,233 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:39:15,245 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:39:15,248 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:39:15,326 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:39:15,400 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-03 10:39:15,878 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}". Retrying still for 3 times.
2025-06-03 10:39:16,145 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}". Retrying still for 2 times.
2025-06-03 10:39:16,409 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}". Retrying still for 1 times.
2025-06-03 10:39:16,670 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}". Giving up.
2025-06-03 10:39:16,673 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 324, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 316, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 2812, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 928, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 917, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11305, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 324, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 316, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 2812, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 928, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 917, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11305, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 324, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 316, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 2812, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 928, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 917, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11305, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 324, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 316, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 2812, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 928, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 917, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/async_support/binance.py", line 11305, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Error in reload_markets due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}
2025-06-03 10:39:16,734 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-06-03 10:42:25,374 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:42:25,376 - root - INFO - Logfile configured
2025-06-03 10:42:25,378 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:42:25,380 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:42:25,382 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:42:25,383 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:42:25,385 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:42:25,386 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:42:25,424 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:42:25,427 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:42:25,431 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:42:25,483 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:42:25,485 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:42:25,492 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:42:25,495 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:42:25,497 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:42:25,500 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:42:25,502 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:42:25,504 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:42:25,507 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:42:25,509 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:42:25,512 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:42:25,515 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:42:25,517 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:42:25,520 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:42:25,522 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:42:25,525 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:42:25,527 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:42:25,530 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:42:25,533 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:42:25,536 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:42:25,538 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:42:25,541 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:42:25,543 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:42:25,546 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:42:25,548 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:42:25,551 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:42:25,554 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:42:25,556 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:42:25,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:42:25,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:42:25,563 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:42:25,565 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:42:25,568 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:42:25,570 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:42:25,573 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:42:25,575 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:42:25,590 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:42:25,592 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:42:25,669 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:42:25,720 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-03 10:42:32,699 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 4 times.
2025-06-03 10:42:32,945 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 3 times.
2025-06-03 10:42:33,191 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 2 times.
2025-06-03 10:42:33,438 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 1 times.
2025-06-03 10:42:33,687 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Giving up.
2025-06-03 10:42:33,689 - freqtrade - ERROR - Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-06-03 10:42:34,050 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:42:34,053 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:43:07,386 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:43:07,388 - root - INFO - Logfile configured
2025-06-03 10:43:07,390 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:43:07,393 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:43:07,394 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:43:07,396 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:43:07,397 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:43:07,399 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:43:07,428 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:43:07,431 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:43:07,435 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:43:07,486 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:43:07,489 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:43:07,495 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:43:07,497 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:43:07,500 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:43:07,502 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:43:07,504 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:43:07,506 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:43:07,508 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:43:07,509 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:43:07,511 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:43:07,512 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:43:07,514 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:43:07,516 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:43:07,517 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:43:07,519 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:43:07,520 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:43:07,522 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:43:07,524 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:43:07,526 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:43:07,528 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:43:07,530 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:43:07,533 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:43:07,534 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:43:07,536 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:43:07,538 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:43:07,540 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:43:07,542 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:43:07,543 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:43:07,545 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:43:07,547 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:43:07,548 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:43:07,550 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:43:07,552 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:43:07,554 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:43:07,556 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:43:07,568 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:43:07,570 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:43:07,650 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:43:07,702 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-03 10:43:14,842 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 4 times.
2025-06-03 10:43:15,091 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 3 times.
2025-06-03 10:43:15,339 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 2 times.
2025-06-03 10:43:15,586 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 1 times.
2025-06-03 10:43:15,832 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Giving up.
2025-06-03 10:43:15,834 - freqtrade - ERROR - Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-06-03 10:43:16,193 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:43:16,196 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:43:48,358 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:43:48,361 - root - INFO - Logfile configured
2025-06-03 10:43:48,363 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:43:48,365 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:43:48,367 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:43:48,368 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:43:48,370 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:43:48,372 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:43:48,399 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:43:48,402 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:43:48,406 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:43:48,459 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:43:48,461 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:43:48,467 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:43:48,469 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:43:48,471 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:43:48,473 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:43:48,475 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:43:48,476 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:43:48,478 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:43:48,480 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:43:48,482 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:43:48,484 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:43:48,486 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:43:48,487 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:43:48,489 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:43:48,490 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:43:48,492 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:43:48,494 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:43:48,495 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:43:48,497 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:43:48,498 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:43:48,500 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:43:48,502 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:43:48,503 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:43:48,505 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:43:48,506 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:43:48,508 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:43:48,509 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:43:48,511 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:43:48,512 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:43:48,514 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:43:48,515 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:43:48,517 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:43:48,518 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:43:48,520 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:43:48,522 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:43:48,541 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:43:48,543 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:43:48,624 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:43:48,690 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-03 10:43:55,752 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 4 times.
2025-06-03 10:43:55,999 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 3 times.
2025-06-03 10:43:56,246 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 2 times.
2025-06-03 10:43:56,493 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Retrying still for 1 times.
2025-06-03 10:43:56,739 - freqtrade.exchange.common - WARNING - get_leverage_tiers() returned exception: "Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}". Giving up.
2025-06-03 10:43:56,742 - freqtrade - ERROR - Could not load leverage tiers due to AuthenticationError. Message: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-06-03 10:43:57,108 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:43:57,110 - root - ERROR - Error while closing connector: ClientConnectionError('Connection lost: SSL shutdown timed out')
2025-06-03 10:50:14,854 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:50:14,856 - root - INFO - Logfile configured
2025-06-03 10:50:14,858 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:50:14,860 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:50:14,862 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:50:14,863 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:50:14,865 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:50:14,867 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:50:14,894 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:50:14,897 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-03 10:50:14,901 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:50:14,953 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-03 10:50:14,956 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:50:14,961 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:50:14,963 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:50:14,966 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:50:14,968 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:50:14,970 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:50:14,972 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:50:14,974 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:50:14,975 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:50:14,977 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:50:14,979 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:50:14,981 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:50:14,982 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:50:14,984 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:50:14,986 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:50:14,987 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:50:14,989 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:50:14,990 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:50:14,992 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:50:14,993 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:50:14,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:50:14,997 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:50:14,999 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:50:15,000 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:50:15,002 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:50:15,003 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:50:15,005 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:50:15,007 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:50:15,008 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:50:15,010 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:50:15,011 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:50:15,013 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:50:15,015 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:50:15,016 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:50:15,018 - freqtrade - ERROR - Fatal exception!
Traceback (most recent call last):
  File "/freqtrade/freqtrade/main.py", line 47, in main
    return_code = args["func"](args)
                  ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/commands/trade_commands.py", line 24, in start_trading
    worker = Worker(args)
             ^^^^^^^^^^^^
  File "/freqtrade/freqtrade/worker.py", line 39, in __init__
    self._init(False)
  File "/freqtrade/freqtrade/worker.py", line 55, in _init
    self.freqtrade = FreqtradeBot(self._config)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/freqtradebot.py", line 98, in __init__
    self.strategy: IStrategy = StrategyResolver.load_strategy(self.config)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/resolvers/strategy_resolver.py", line 94, in load_strategy
    StrategyResolver._strategy_sanity_validations(strategy)
  File "/freqtrade/freqtrade/resolvers/strategy_resolver.py", line 167, in _strategy_sanity_validations
    raise ImportError(
ImportError: Short strategies cannot run in spot markets. Please make sure that this is the correct strategy and that your trading mode configuration is correct. You can run this strategy in spot markets by setting `can_short=False` in your strategy. Please note that short signals will be ignored in that case.
2025-06-03 10:53:28,474 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:53:28,476 - root - INFO - Logfile configured
2025-06-03 10:53:28,478 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:53:28,480 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:53:28,482 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:53:28,483 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:53:28,485 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:53:28,487 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:53:28,523 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:53:28,526 - freqtrade.configuration.directory_operations - INFO - Created data directory: None
2025-06-03 10:53:28,527 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/gateio ...
2025-06-03 10:53:28,531 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:53:28,566 - freqtrade.exchange.check_exchange - INFO - Exchange "gateio" is officially supported by the Freqtrade development team.
2025-06-03 10:53:28,568 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:53:28,574 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:53:28,576 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:53:28,578 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:53:28,580 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:53:28,582 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:53:28,583 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:53:28,585 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:53:28,587 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:53:28,588 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:53:28,590 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:53:28,592 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:53:28,595 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:53:28,597 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:53:28,599 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:53:28,601 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:53:28,603 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:53:28,604 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:53:28,606 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:53:28,608 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:53:28,610 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:53:28,611 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:53:28,613 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:53:28,614 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:53:28,616 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:53:28,618 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:53:28,619 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:53:28,621 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:53:28,623 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:53:28,624 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:53:28,626 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:53:28,627 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:53:28,629 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:53:28,631 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:53:28,632 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:53:28,644 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:53:28,646 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:53:28,705 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:53:28,741 - freqtrade.exchange.exchange - INFO - Using Exchange "Gate.io"
2025-06-03 10:53:43,300 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 4 times.
2025-06-03 10:53:43,591 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 3 times.
2025-06-03 10:53:43,872 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 2 times.
2025-06-03 10:53:44,151 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 1 times.
2025-06-03 10:53:44,430 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Giving up.
2025-06-03 10:53:44,433 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 694, in reload_markets
    self._trading_fees = self.fetch_trading_fees()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 1 more time]
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}
2025-06-03 10:53:46,370 - freqtrade.exchange.gate - INFO - Gate: Classic account.
2025-06-03 10:53:46,372 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Gate'...
2025-06-03 10:53:46,762 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 4 times.
2025-06-03 10:53:47,043 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 3 times.
2025-06-03 10:53:47,324 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 2 times.
2025-06-03 10:53:47,605 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 1 times.
2025-06-03 10:53:47,885 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Giving up.
2025-06-03 10:53:47,888 - freqtrade - ERROR - Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}
2025-06-03 10:56:35,940 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:56:35,943 - root - INFO - Logfile configured
2025-06-03 10:56:35,945 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:56:35,947 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:56:35,949 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:56:35,951 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:56:35,953 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:56:35,955 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:56:35,989 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:56:35,991 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/gateio ...
2025-06-03 10:56:35,997 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:56:36,052 - freqtrade.exchange.check_exchange - INFO - Exchange "gateio" is officially supported by the Freqtrade development team.
2025-06-03 10:56:36,054 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:56:36,062 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:56:36,066 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:56:36,069 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:56:36,072 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:56:36,074 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:56:36,075 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:56:36,078 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:56:36,081 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:56:36,083 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:56:36,084 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:56:36,086 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:56:36,088 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:56:36,089 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:56:36,091 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:56:36,092 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:56:36,094 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:56:36,095 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:56:36,097 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:56:36,099 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:56:36,100 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:56:36,102 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:56:36,104 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:56:36,105 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:56:36,107 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:56:36,109 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:56:36,110 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:56:36,112 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:56:36,114 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:56:36,115 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:56:36,117 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:56:36,119 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:56:36,120 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:56:36,122 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:56:36,124 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:56:36,135 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:56:36,137 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:56:36,194 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:56:36,231 - freqtrade.exchange.exchange - INFO - Using Exchange "Gate.io"
2025-06-03 10:56:50,106 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 4 times.
2025-06-03 10:56:50,361 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 3 times.
2025-06-03 10:56:50,616 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 2 times.
2025-06-03 10:56:50,875 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 1 times.
2025-06-03 10:56:51,129 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Giving up.
2025-06-03 10:56:51,131 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 694, in reload_markets
    self._trading_fees = self.fetch_trading_fees()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 1 more time]
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}
2025-06-03 10:56:52,930 - freqtrade.exchange.gate - INFO - Gate: Classic account.
2025-06-03 10:56:52,932 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Gate'...
2025-06-03 10:56:53,302 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 4 times.
2025-06-03 10:56:53,557 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 3 times.
2025-06-03 10:56:53,814 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 2 times.
2025-06-03 10:56:54,069 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 1 times.
2025-06-03 10:56:54,325 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Giving up.
2025-06-03 10:56:54,328 - freqtrade - ERROR - Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}
2025-06-03 10:58:30,981 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 10:58:30,983 - root - INFO - Logfile configured
2025-06-03 10:58:30,986 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 10:58:30,988 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 10:58:30,990 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 10:58:30,991 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 10:58:30,993 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 10:58:30,995 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 10:58:31,031 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 10:58:31,034 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/gateio ...
2025-06-03 10:58:31,039 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 10:58:31,080 - freqtrade.exchange.check_exchange - INFO - Exchange "gateio" is officially supported by the Freqtrade development team.
2025-06-03 10:58:31,082 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 10:58:31,088 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 10:58:31,090 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 10:58:31,092 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 10:58:31,094 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 10:58:31,096 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 10:58:31,097 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 10:58:31,099 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 10:58:31,101 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 10:58:31,103 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 10:58:31,106 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 10:58:31,109 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 10:58:31,111 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 10:58:31,114 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 10:58:31,116 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 10:58:31,119 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 10:58:31,121 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 10:58:31,123 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 10:58:31,126 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 10:58:31,128 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 10:58:31,131 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 10:58:31,133 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 10:58:31,134 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 10:58:31,136 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 10:58:31,137 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 10:58:31,139 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 10:58:31,140 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 10:58:31,142 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 10:58:31,144 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 10:58:31,145 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 10:58:31,147 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 10:58:31,149 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 10:58:31,150 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 10:58:31,152 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 10:58:31,153 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 10:58:31,165 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 10:58:31,167 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:58:31,223 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 10:58:31,257 - freqtrade.exchange.exchange - INFO - Using Exchange "Gate.io"
2025-06-03 10:58:45,878 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 4 times.
2025-06-03 10:58:46,124 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 3 times.
2025-06-03 10:58:46,370 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 2 times.
2025-06-03 10:58:46,614 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 1 times.
2025-06-03 10:58:46,858 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Giving up.
2025-06-03 10:58:46,860 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 694, in reload_markets
    self._trading_fees = self.fetch_trading_fees()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 1 more time]
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}
2025-06-03 10:58:48,602 - freqtrade.exchange.gate - INFO - Gate: Classic account.
2025-06-03 10:58:48,604 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Gate'...
2025-06-03 10:58:49,019 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 4 times.
2025-06-03 10:58:49,267 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 3 times.
2025-06-03 10:58:49,513 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 2 times.
2025-06-03 10:58:49,760 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 1 times.
2025-06-03 10:58:50,006 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Giving up.
2025-06-03 10:58:50,008 - freqtrade - ERROR - Could not get balance due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}
2025-06-03 11:01:16,756 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 11:01:16,758 - root - INFO - Logfile configured
2025-06-03 11:01:16,760 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 11:01:16,763 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 11:01:16,765 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 11:01:16,767 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 11:01:16,769 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 11:01:16,770 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 11:01:16,800 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 11:01:16,803 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/gateio ...
2025-06-03 11:01:16,807 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 11:01:16,843 - freqtrade.exchange.check_exchange - INFO - Exchange "gateio" is officially supported by the Freqtrade development team.
2025-06-03 11:01:16,845 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 11:01:16,853 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 11:01:16,855 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 11:01:16,858 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 11:01:16,860 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 11:01:16,862 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 11:01:16,863 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 11:01:16,865 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 11:01:16,867 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 11:01:16,869 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 11:01:16,870 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 11:01:16,872 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 11:01:16,874 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 11:01:16,875 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 11:01:16,878 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 11:01:16,880 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 11:01:16,881 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 11:01:16,883 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 11:01:16,885 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 11:01:16,887 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 11:01:16,889 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 11:01:16,890 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 11:01:16,892 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 11:01:16,893 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 11:01:16,895 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 11:01:16,897 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 11:01:16,898 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 11:01:16,900 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 11:01:16,901 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 11:01:16,903 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 11:01:16,904 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 11:01:16,906 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 11:01:16,908 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 11:01:16,909 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 11:01:16,911 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 11:01:16,922 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 11:01:16,924 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 11:01:16,979 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 11:01:17,013 - freqtrade.exchange.exchange - INFO - Using Exchange "Gate.io"
2025-06-03 11:01:31,411 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 4 times.
2025-06-03 11:01:31,672 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 3 times.
2025-06-03 11:01:31,933 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 2 times.
2025-06-03 11:01:32,193 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 1 times.
2025-06-03 11:01:32,455 - freqtrade.exchange.common - WARNING - fetch_trading_fees() returned exception: "Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Giving up.
2025-06-03 11:01:32,458 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.gateio.ws/api/v4/wallet/fee

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1818, in fetch_trading_fees
    trading_fees: dict[str, Any] = self._api.fetch_trading_fees()
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 2226, in fetch_trading_fees
    response = self.privateWalletGetFee(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4487, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4483, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4472, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/gate.py", line 7743, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], label, feedback)
  File "/home/<USER>/.local/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4876, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 694, in reload_markets
    self._trading_fees = self.fetch_trading_fees()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 199, in wrapper
    return wrapper(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 1 more time]
  File "/freqtrade/freqtrade/exchange/common.py", line 202, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 1824, in fetch_trading_fees
    raise TemporaryError(
freqtrade.exceptions.TemporaryError: Could not fetch trading fees due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}
2025-06-03 11:01:34,308 - freqtrade.exchange.gate - INFO - Gate: Classic account.
2025-06-03 11:01:34,310 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Gate'...
2025-06-03 11:01:34,681 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 4 times.
2025-06-03 11:01:34,941 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 3 times.
2025-06-03 11:01:35,202 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"message":"Request IP not in whitelist: ************","label":"FORBIDDEN"}". Retrying still for 2 times.
2025-06-03 11:01:35,463 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Retrying still for 1 times.
2025-06-03 11:01:35,724 - freqtrade.exchange.common - WARNING - get_balances() returned exception: "Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}". Giving up.
2025-06-03 11:01:35,726 - freqtrade - ERROR - Could not get balance due to PermissionDenied. Message: gateio {"label":"FORBIDDEN","message":"Request IP not in whitelist: ************"}
2025-06-03 11:04:38,053 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-03 11:04:38,055 - root - INFO - Logfile configured
2025-06-03 11:04:38,057 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-03 11:04:38,060 - freqtrade.configuration.configuration - INFO - Runmode set to live.
2025-06-03 11:04:38,062 - freqtrade.configuration.configuration - WARNING - `force_entry_enable` RPC message enabled.
2025-06-03 11:04:38,063 - freqtrade.configuration.configuration - INFO - Dry run is disabled
2025-06-03 11:04:38,065 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.sqlite"
2025-06-03 11:04:38,067 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 1 ...
2025-06-03 11:04:38,096 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-03 11:04:38,098 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/gateio ...
2025-06-03 11:04:38,103 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-03 11:04:38,140 - freqtrade.exchange.check_exchange - INFO - Exchange "gateio" is officially supported by the Freqtrade development team.
2025-06-03 11:04:38,142 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-03 11:04:38,148 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy AODualSideStrategy from '/freqtrade/user_data/strategies/ao_strategy.py'...
2025-06-03 11:04:38,151 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-03 11:04:38,153 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-03 11:04:38,155 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDC.
2025-06-03 11:04:38,157 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-06-03 11:04:38,159 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-06-03 11:04:38,162 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-06-03 11:04:38,164 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-06-03 11:04:38,166 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: True.
2025-06-03 11:04:38,168 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 1.
2025-06-03 11:04:38,169 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 3.0}
2025-06-03 11:04:38,171 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-03 11:04:38,173 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-06-03 11:04:38,175 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-06-03 11:04:38,177 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-06-03 11:04:38,179 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-03 11:04:38,181 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-03 11:04:38,183 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-03 11:04:38,185 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'emergency_exit': 'market', 'force_exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-06-03 11:04:38,187 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-03 11:04:38,189 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDC
2025-06-03 11:04:38,190 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-06-03 11:04:38,192 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 60
2025-06-03 11:04:38,194 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-03 11:04:38,196 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-03 11:04:38,198 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-03 11:04:38,199 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: True
2025-06-03 11:04:38,201 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-03 11:04:38,203 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-03 11:04:38,204 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-03 11:04:38,206 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-03 11:04:38,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-03 11:04:38,209 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 1
2025-06-03 11:04:38,211 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-03 11:04:38,224 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.86
2025-06-03 11:04:38,226 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 11:04:38,287 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-06-03 11:04:38,324 - freqtrade.exchange.exchange - INFO - Using Exchange "Gate.io"
2025-06-03 11:04:54,914 - freqtrade.exchange.gate - INFO - Gate: Classic account.
2025-06-03 11:04:54,917 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Gate'...
2025-06-03 11:04:55,570 - freqtrade.wallets - INFO - Wallets synced.
2025-06-03 11:04:55,620 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-03 11:04:55,622 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-06-03 11:04:55,651 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair ETH/USDC:USDC is not compatible with exchange Gate.io. Removing it from whitelist..
2025-06-03 11:04:55,675 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 0 pairs: []
2025-06-03 11:04:55,677 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.05s
2025-06-03 11:04:55,683 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-03 11:04:55,687 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-03 11:04:55,691 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-03 11:04:55,694 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-06-03 11:04:55,697 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'stopped'}
2025-06-03 11:04:55,699 - freqtrade.worker - INFO - Changing state to: STOPPED
2025-06-03 11:04:55,734 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "1 open trades active.\n\nHandle these trades manually on Gate.io, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \n"}
2025-06-03 11:05:00,737 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-03 11:06:00,742 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-03 11:07:00,747 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-03 11:08:00,756 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-03 11:09:00,760 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='docker-2025.6-dev-f5cb486f', state='STOPPED'
2025-06-03 11:09:31,322 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-06-03 11:09:31,324 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-06-03 11:09:31,326 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-06-03 11:09:31,341 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "1 open trades active.\n\nHandle these trades manually on Gate.io, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \n"}
2025-06-03 11:09:31,345 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-06-03 11:09:31,603 - freqtrade - INFO - SIGINT received, aborting ...

from freqtrade.strategy import IStrategy, DecimalParameter, IntParameter, BooleanParameter
from datetime import datetime
import pandas as pd
import numpy as np
import talib.abstract as ta
from technical import qtpylib
from typing import Optional

class OptimizedMultiIndicatorStrategy(IStrategy):
    INTERFACE_VERSION = 3

    timeframe = '15m'  # Changed to 15m as per your request

    # Optimizable parameters for hyperopt
    # RSI parameters
    rsi_period = IntParameter(10, 20, default=14, space="buy")
    rsi_overbought = IntParameter(65, 80, default=70, space="sell")
    rsi_oversold = IntParameter(20, 35, default=30, space="buy")

    # MACD parameters
    macd_fast = IntParameter(8, 16, default=12, space="buy")
    macd_slow = IntParameter(20, 30, default=26, space="buy")
    macd_signal = IntParameter(7, 12, default=9, space="buy")

    # EMA parameters
    ema_short = IntParameter(15, 25, default=21, space="buy")
    ema_medium = IntParameter(45, 55, default=50, space="buy")
    ema_long = IntParameter(180, 220, default=200, space="buy")

    # Bollinger Bands parameters
    bb_period = IntParameter(15, 25, default=20, space="buy")
    bb_std = DecimalParameter(1.8, 2.5, default=2.0, space="buy")

    # Stochastic parameters
    stoch_k = IntParameter(10, 18, default=14, space="buy")
    stoch_d = IntParameter(3, 7, default=3, space="buy")

    # ADX parameters
    adx_period = IntParameter(10, 20, default=14, space="buy")
    adx_threshold = IntParameter(20, 30, default=25, space="buy")

    # Volume parameters
    volume_factor = DecimalParameter(1.2, 2.5, default=1.5, space="buy")

    # Risk management parameters
    use_dynamic_sl = BooleanParameter(default=True, space="protection")
    atr_multiplier = DecimalParameter(1.5, 3.0, default=2.0, space="protection")

    # Use market orders for both entry and exit
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'emergency_exit': 'market',
        'force_exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # More conservative ROI and stoploss
    minimal_roi = {
        "0": 3.0,    # 15% max profit
    }
    stoploss = -0.08  # 8% stop loss (much more conservative)
    startup_candle_count: int = 250  # Increased for more indicators

    can_short = True  # Enable SHORT entries
    position_adjustment_enable = False  # Only one position active

    # Trailing stop
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.04
    trailing_only_offset_is_reached = True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None, side: str,
                 **kwargs) -> float:
        max_leverage = 5
        return max_leverage

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Comprehensive multi-indicator analysis combining trend, momentum, volatility, and volume
        """

        # Trend Analysis - Multiple EMAs
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=self.ema_short.value)
        dataframe['ema_medium'] = ta.EMA(dataframe, timeperiod=self.ema_medium.value)
        dataframe['ema_long'] = ta.EMA(dataframe, timeperiod=self.ema_long.value)

        # Momentum Indicators
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # MACD
        macd = ta.MACD(dataframe,
                      fastperiod=self.macd_fast.value,
                      slowperiod=self.macd_slow.value,
                      signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macd_signal'] = macd['macdsignal']
        dataframe['macd_hist'] = macd['macdhist']

        # Stochastic Oscillator
        stoch = ta.STOCH(dataframe,
                        fastk_period=self.stoch_k.value,
                        slowk_period=self.stoch_d.value,
                        slowd_period=self.stoch_d.value)
        dataframe['stoch_k'] = stoch['slowk']
        dataframe['stoch_d'] = stoch['slowd']

        # ADX for trend strength
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)

        # Volatility Indicators
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(dataframe['close'],
                                          window=self.bb_period.value,
                                          stds=self.bb_std.value)
        dataframe['bb_lower'] = bollinger['lower']
        dataframe['bb_middle'] = bollinger['mid']
        dataframe['bb_upper'] = bollinger['upper']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        dataframe['bb_percent'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])

        # ATR for volatility and dynamic stop loss
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # Volume Analysis
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_sma'] = dataframe['obv'].rolling(window=20).mean()

        # Keep original AO for reference
        median_price = (dataframe['high'] + dataframe['low']) / 2
        dataframe['ao_fast'] = median_price.rolling(window=5).mean()
        dataframe['ao_slow'] = median_price.rolling(window=34).mean()
        dataframe['ao'] = dataframe['ao_fast'] - dataframe['ao_slow']

        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Multi-indicator entry logic combining trend, momentum, volatility, and volume confirmation
        """
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        # LONG ENTRY CONDITIONS
        # Primary trend condition: EMAs aligned bullishly
        long_trend = (
            (dataframe['ema_short'] > dataframe['ema_medium']) &
            (dataframe['ema_medium'] > dataframe['ema_long']) &
            (dataframe['close'] > dataframe['ema_short'])
        )

        # Momentum conditions
        long_momentum = (
            # RSI oversold but recovering
            (dataframe['rsi'] > self.rsi_oversold.value) &
            (dataframe['rsi'] < 60) &
            (dataframe['rsi'] > dataframe['rsi'].shift(1)) |

            # MACD bullish
            (dataframe['macd'] > dataframe['macd_signal']) &
            (dataframe['macd_hist'] > dataframe['macd_hist'].shift(1)) |

            # Stochastic oversold and turning up
            (dataframe['stoch_k'] > dataframe['stoch_d']) &
            (dataframe['stoch_k'] > dataframe['stoch_k'].shift(1))
        )

        # Volatility conditions
        long_volatility = (
            # Price near lower BB but not oversold
            (dataframe['bb_percent'] > 0.1) &
            (dataframe['bb_percent'] < 0.4) &
            (dataframe['close'] > dataframe['bb_lower']) |

            # BB squeeze ending (expanding)
            (dataframe['bb_width'] > dataframe['bb_width'].shift(1))
        )

        # Volume confirmation
        long_volume = (
            (dataframe['volume_ratio'] > self.volume_factor.value) &
            (dataframe['obv'] > dataframe['obv_sma'])
        )

        # Trend strength
        long_strength = (dataframe['adx'] > self.adx_threshold.value)

        # Combine all conditions for LONG entry
        dataframe.loc[
            long_trend & long_momentum & long_volatility & long_volume & long_strength,
            ['enter_long', 'enter_tag']
        ] = (1, 'multi_indicator_long')

        # SHORT ENTRY CONDITIONS
        # Primary trend condition: EMAs aligned bearishly
        short_trend = (
            (dataframe['ema_short'] < dataframe['ema_medium']) &
            (dataframe['ema_medium'] < dataframe['ema_long']) &
            (dataframe['close'] < dataframe['ema_short'])
        )

        # Momentum conditions
        short_momentum = (
            # RSI overbought but declining
            (dataframe['rsi'] < self.rsi_overbought.value) &
            (dataframe['rsi'] > 40) &
            (dataframe['rsi'] < dataframe['rsi'].shift(1)) |

            # MACD bearish
            (dataframe['macd'] < dataframe['macd_signal']) &
            (dataframe['macd_hist'] < dataframe['macd_hist'].shift(1)) |

            # Stochastic overbought and turning down
            (dataframe['stoch_k'] < dataframe['stoch_d']) &
            (dataframe['stoch_k'] < dataframe['stoch_k'].shift(1))
        )

        # Volatility conditions
        short_volatility = (
            # Price near upper BB but not overbought
            (dataframe['bb_percent'] < 0.9) &
            (dataframe['bb_percent'] > 0.6) &
            (dataframe['close'] < dataframe['bb_upper']) |

            # BB squeeze ending (expanding)
            (dataframe['bb_width'] > dataframe['bb_width'].shift(1))
        )

        # Volume confirmation
        short_volume = (
            (dataframe['volume_ratio'] > self.volume_factor.value) &
            (dataframe['obv'] < dataframe['obv_sma'])
        )

        # Trend strength
        short_strength = (dataframe['adx'] > self.adx_threshold.value)

        # Combine all conditions for SHORT entry
        dataframe.loc[
            short_trend & short_momentum & short_volatility & short_volume & short_strength,
            ['enter_short', 'enter_tag']
        ] = (1, 'multi_indicator_short')

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Multi-indicator exit logic for profit taking and loss prevention
        """
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # LONG EXIT CONDITIONS
        # Exit when trend reverses
        long_exit_trend = (
            (dataframe['ema_short'] < dataframe['ema_medium']) |
            (dataframe['close'] < dataframe['ema_short'])
        )

        # Exit when momentum turns bearish
        long_exit_momentum = (
            # RSI overbought
            (dataframe['rsi'] > self.rsi_overbought.value) |

            # MACD bearish crossover
            (qtpylib.crossed_below(dataframe['macd'], dataframe['macd_signal'])) |

            # Stochastic overbought and turning down
            ((dataframe['stoch_k'] > 80) & (dataframe['stoch_k'] < dataframe['stoch_k'].shift(1)))
        )

        # Exit when price hits upper BB
        long_exit_volatility = (
            (dataframe['bb_percent'] > 0.95) |
            (dataframe['close'] > dataframe['bb_upper'])
        )

        # Exit when volume dries up
        long_exit_volume = (
            (dataframe['volume_ratio'] < 0.8) &
            (dataframe['obv'] < dataframe['obv_sma'])
        )

        # Combine exit conditions for LONG
        dataframe.loc[
            long_exit_trend | long_exit_momentum | long_exit_volatility | long_exit_volume,
            ['exit_long', 'exit_tag']
        ] = (1, 'multi_indicator_exit_long')

        # SHORT EXIT CONDITIONS
        # Exit when trend reverses
        short_exit_trend = (
            (dataframe['ema_short'] > dataframe['ema_medium']) |
            (dataframe['close'] > dataframe['ema_short'])
        )

        # Exit when momentum turns bullish
        short_exit_momentum = (
            # RSI oversold
            (dataframe['rsi'] < self.rsi_oversold.value) |

            # MACD bullish crossover
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macd_signal'])) |

            # Stochastic oversold and turning up
            ((dataframe['stoch_k'] < 20) & (dataframe['stoch_k'] > dataframe['stoch_k'].shift(1)))
        )

        # Exit when price hits lower BB
        short_exit_volatility = (
            (dataframe['bb_percent'] < 0.05) |
            (dataframe['close'] < dataframe['bb_lower'])
        )

        # Exit when volume dries up
        short_exit_volume = (
            (dataframe['volume_ratio'] < 0.8) &
            (dataframe['obv'] > dataframe['obv_sma'])
        )

        # Combine exit conditions for SHORT
        dataframe.loc[
            short_exit_trend | short_exit_momentum | short_exit_volatility | short_exit_volume,
            ['exit_short', 'exit_tag']
        ] = (1, 'multi_indicator_exit_short')

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Dynamic stop loss based on ATR
        """
        if not self.use_dynamic_sl.value:
            return self.stoploss

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if trade.is_short:
            # For short positions, stop loss is above entry
            dynamic_sl = (last_candle['atr'] * self.atr_multiplier.value) / trade.open_rate
        else:
            # For long positions, stop loss is below entry
            dynamic_sl = -(last_candle['atr'] * self.atr_multiplier.value) / trade.open_rate

        # Don't make stop loss worse than the fixed one
        if trade.is_short:
            return max(dynamic_sl, -abs(self.stoploss))
        else:
            return max(dynamic_sl, self.stoploss)
